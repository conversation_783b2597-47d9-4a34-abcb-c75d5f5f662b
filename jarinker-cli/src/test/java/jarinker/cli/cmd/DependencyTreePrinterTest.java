package jarinker.cli.cmd;

import static org.assertj.core.api.Assertions.assertThat;

import jarinker.core.AnalyzerType;
import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

class DependencyTreePrinterTest {

    private ByteArrayOutputStream outputStream;
    private PrintStream originalOut;

    @BeforeEach
    void setUp() {
        outputStream = new ByteArrayOutputStream();
        originalOut = System.out;
        System.setOut(new PrintStream(outputStream));
    }

    @AfterEach
    void tearDown() {
        System.setOut(originalOut);
    }

    private String getOutput() {
        return outputStream.toString();
    }

    @Nested
    class PrintDependencyTreeTests {

        @Test
        void shouldPrintEmptyMessageWhenNoDependencies() {
            // Arrange
            var dependenciesMap = Map.<String, Set<String>>of();

            // Act
            DependencyTreePrinter.printDependencyTree(dependenciesMap, false, AnalyzerType.CLASS);

            // Assert
            String output = getOutput();
            assertThat(output).contains("🔍 No dependencies found.");
        }

        @Test
        void shouldPrintSimpleTreeWithSingleDependency() {
            // Arrange
            var dependenciesMap = Map.of(
                    "quick-start-0.1.0.jar/com.example.QuickStart",
                            Set.of("com.google.common/com.google.common.base.Strings"),
                    "com.google.common/com.google.common.base.Strings", Set.<String>of());

            // Act
            DependencyTreePrinter.printDependencyTree(dependenciesMap, false, AnalyzerType.CLASS);

            // Assert
            String output = getOutput();
            assertThat(output).contains("🌳 Dependency Tree:");
            assertThat(output).contains("📦 quick-start-0.1.0.jar/com.example.QuickStart");
            assertThat(output).contains("└── com.google.common/com.google.common.base.Strings");
        }

        @Test
        void shouldPrintTreeWithMultipleDependencies() {
            // Arrange
            var dependenciesMap = Map.of(
                    "quick-start-0.1.0.jar/com.example.QuickStart",
                            Set.of(
                                    "com.google.common/com.google.common.base.Strings",
                                    "com.google.common/com.google.common.collect.Lists"),
                    "com.google.common/com.google.common.base.Strings", Set.<String>of(),
                    "com.google.common/com.google.common.collect.Lists", Set.<String>of());

            // Act
            DependencyTreePrinter.printDependencyTree(dependenciesMap, false, AnalyzerType.CLASS);

            // Assert
            String output = getOutput();
            assertThat(output).contains("🌳 Dependency Tree:");
            assertThat(output).contains("📦 quick-start-0.1.0.jar/com.example.QuickStart");
            assertThat(output).contains("├── com.google.common/com.google.common.base.Strings");
            assertThat(output).contains("└── com.google.common/com.google.common.collect.Lists");
        }

        @Test
        void shouldShowAlreadyShownForDuplicateDependencies() {
            // Arrange
            var dependenciesMap = Map.of(
                    "quick-start-0.1.0.jar/com.example.QuickStart",
                            Set.of(
                                    "com.google.common/com.google.common.base.Strings",
                                    "quick-start-0.1.0.jar/com.example.util.StringUtils"),
                    "quick-start-0.1.0.jar/com.example.util.StringUtils",
                            Set.of("com.google.common/com.google.common.base.Strings"),
                    "com.google.common/com.google.common.base.Strings", Set.<String>of());

            // Act
            DependencyTreePrinter.printDependencyTree(dependenciesMap, false, AnalyzerType.CLASS);

            // Assert
            String output = getOutput();
            assertThat(output).contains("🌳 Dependency Tree:");
            assertThat(output).contains("📦 quick-start-0.1.0.jar/com.example.QuickStart");
            assertThat(output).contains("├── com.google.common/com.google.common.base.Strings");
            assertThat(output).contains("└── quick-start-0.1.0.jar/com.example.util.StringUtils");
            assertThat(output).contains("└── com.google.common/com.google.common.base.Strings (already shown)");
        }

        @Test
        void shouldFilterJdkDependenciesWhenShowJdkDepsIsFalse() {
            // Arrange
            var dependenciesMap = Map.of(
                    "quick-start-0.1.0.jar/com.example.QuickStart",
                            Set.of("java.lang.String", "com.google.common/com.google.common.base.Strings"),
                    "java.lang.String", Set.<String>of(),
                    "com.google.common/com.google.common.base.Strings", Set.<String>of());

            // Act
            DependencyTreePrinter.printDependencyTree(dependenciesMap, false, AnalyzerType.CLASS);

            // Assert
            String output = getOutput();
            assertThat(output).contains("com.google.common/com.google.common.base.Strings");
            assertThat(output).doesNotContain("java.lang.String");
        }

        @Test
        void shouldShowJdkDependenciesWhenShowJdkDepsIsTrue() {
            // Arrange
            var dependenciesMap = Map.of(
                    "quick-start-0.1.0.jar/com.example.QuickStart",
                            Set.of("java.lang.String", "com.google.common/com.google.common.base.Strings"),
                    "java.lang.String", Set.<String>of(),
                    "com.google.common/com.google.common.base.Strings", Set.<String>of());

            // Act
            DependencyTreePrinter.printDependencyTree(dependenciesMap, true, AnalyzerType.CLASS);

            // Assert
            String output = getOutput();
            assertThat(output).contains("com.google.common/com.google.common.base.Strings");
            assertThat(output).contains("java.lang.String");
        }

        @Test
        void shouldFilterSamePackageDependenciesForPackageAnalysis() {
            // Arrange
            var dependenciesMap = Map.of(
                    "quick-start-0.1.0.jar/com.example",
                            Set.of(
                                    "quick-start-0.1.0.jar/com.example.util",
                                    "com.google.common/com.google.common.base"),
                    "quick-start-0.1.0.jar/com.example.util", Set.<String>of(),
                    "com.google.common/com.google.common.base", Set.<String>of());

            // Act
            DependencyTreePrinter.printDependencyTree(dependenciesMap, false, AnalyzerType.PACKAGE);

            // Assert
            String output = getOutput();
            assertThat(output).contains("com.google.common/com.google.common.base");
            assertThat(output).doesNotContain("quick-start-0.1.0.jar/com.example.util");
        }

        @Test
        void shouldHandleComplexTreeWithMultipleLevels() {
            // Arrange
            var dependenciesMap = Map.of(
                    "quick-start-0.1.0.jar/com.example.QuickStart",
                            Set.of("quick-start-0.1.0.jar/com.example.util.StringUtils"),
                    "quick-start-0.1.0.jar/com.example.util.StringUtils",
                            Set.of("com.google.common/com.google.common.base.Strings"),
                    "com.google.common/com.google.common.base.Strings", Set.of("java.lang.String"),
                    "java.lang.String", Set.<String>of());

            // Act
            DependencyTreePrinter.printDependencyTree(dependenciesMap, true, AnalyzerType.CLASS);

            // Assert
            String output = getOutput();
            assertThat(output).contains("🌳 Dependency Tree:");
            assertThat(output).contains("📦 quick-start-0.1.0.jar/com.example.QuickStart");
            assertThat(output).contains("└── quick-start-0.1.0.jar/com.example.util.StringUtils");
            assertThat(output).contains("    └── com.google.common/com.google.common.base.Strings");
            assertThat(output).contains("        └── java.lang.String");
        }
    }
}

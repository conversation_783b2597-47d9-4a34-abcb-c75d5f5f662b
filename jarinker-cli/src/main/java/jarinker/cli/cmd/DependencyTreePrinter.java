package jarinker.cli.cmd;

import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Utility class for printing dependency trees with deduplication and shortest path selection.
 *
 * <AUTHOR>
 */
public class DependencyTreePrinter {

    /**
     * Print dependency tree with deduplication and shortest path selection.
     *
     * @param dependenciesMap the dependencies map from DependencyGraph
     * @param showJdkDeps whether to show JDK dependencies
     * @param analysisType the analysis type for scope filtering
     */
    public static void printDependencyTree(
            Map<String, Set<String>> dependenciesMap, boolean showJdkDeps, jarinker.core.AnalyzerType analysisType) {

        if (dependenciesMap.isEmpty()) {
            System.out.println("🔍 No dependencies found.");
            return;
        }

        System.out.println("🌳 Dependency Tree:");
        System.out.println();

        // Filter dependencies based on settings
        Map<String, Set<String>> filteredMap = filterDependencies(dependenciesMap, showJdkDeps, analysisType);

        if (filteredMap.isEmpty()) {
            System.out.println("🔍 No dependencies found after filtering.");
            return;
        }

        // Calculate shortest paths for all nodes
        Map<String, Integer> shortestPaths = calculateShortestPaths(filteredMap);

        // Track nodes that have been fully shown
        Set<String> alreadyShown = new HashSet<>();

        // Print trees for each root node (nodes that have dependencies)
        // Only print nodes that are not dependencies of other nodes (true roots)
        Set<String> allDependencies =
                filteredMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());

        filteredMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEach(entry -> {
            String root = entry.getKey();
            if (!entry.getValue().isEmpty() && !allDependencies.contains(root)) {
                printNodeTree(root, filteredMap, alreadyShown, "");
                System.out.println();
            }
        });
    }

    /**
     * Filter dependencies based on JDK and scope settings.
     */
    private static Map<String, Set<String>> filterDependencies(
            Map<String, Set<String>> dependenciesMap, boolean showJdkDeps, jarinker.core.AnalyzerType analysisType) {

        Map<String, Set<String>> filtered = new HashMap<>();

        for (Map.Entry<String, Set<String>> entry : dependenciesMap.entrySet()) {
            String source = entry.getKey();
            Set<String> dependencies = entry.getValue();

            Set<String> filteredDeps = dependencies.stream()
                    .filter(dep -> showJdkDeps || !isJdkDependency(dep))
                    .filter(dep -> !isSameScopeAsSelf(source, dep, analysisType))
                    .collect(Collectors.toSet());

            if (!filteredDeps.isEmpty()) {
                filtered.put(source, filteredDeps);
            }
        }

        return filtered;
    }

    /**
     * Calculate shortest paths from all nodes to all other nodes using BFS.
     */
    private static Map<String, Integer> calculateShortestPaths(Map<String, Set<String>> graph) {
        Map<String, Integer> shortestPaths = new HashMap<>();

        // Initialize all nodes with maximum distance
        Set<String> allNodes = new HashSet<>(graph.keySet());
        graph.values().forEach(allNodes::addAll);

        for (String node : allNodes) {
            shortestPaths.put(node, Integer.MAX_VALUE);
        }

        // For each root node, calculate shortest paths using BFS
        for (String root : graph.keySet()) {
            if (!graph.get(root).isEmpty()) {
                bfsShortestPath(root, graph, shortestPaths);
            }
        }

        return shortestPaths;
    }

    /**
     * BFS to calculate shortest paths from a root node.
     */
    private static void bfsShortestPath(
            String root, Map<String, Set<String>> graph, Map<String, Integer> shortestPaths) {
        Queue<String> queue = new LinkedList<>();
        Map<String, Integer> distances = new HashMap<>();

        queue.offer(root);
        distances.put(root, 0);

        while (!queue.isEmpty()) {
            String current = queue.poll();
            int currentDistance = distances.get(current);

            Set<String> neighbors = graph.getOrDefault(current, Set.of());
            for (String neighbor : neighbors) {
                int newDistance = currentDistance + 1;

                if (!distances.containsKey(neighbor) || newDistance < distances.get(neighbor)) {
                    distances.put(neighbor, newDistance);
                    queue.offer(neighbor);

                    // Update global shortest path if this is shorter
                    if (newDistance < shortestPaths.get(neighbor)) {
                        shortestPaths.put(neighbor, newDistance);
                    }
                }
            }
        }
    }

    /**
     * Print dependency tree for a node with proper tree formatting.
     */
    private static void printNodeTree(
            String node, Map<String, Set<String>> graph, Set<String> alreadyShown, String prefix) {

        System.out.println("📦 " + node);

        Set<String> dependencies = graph.getOrDefault(node, Set.of());
        if (dependencies.isEmpty()) {
            return;
        }

        // Sort dependencies for consistent output
        List<String> sortedDeps = dependencies.stream().sorted().collect(Collectors.toList());

        for (int i = 0; i < sortedDeps.size(); i++) {
            String dep = sortedDeps.get(i);
            boolean isLast = (i == sortedDeps.size() - 1);

            String childPrefix = isLast ? "└── " : "├── ";
            String nextPrefix = isLast ? "    " : "│   ";

            if (alreadyShown.contains(dep)) {
                System.out.println(childPrefix + dep + " (already shown)");
            } else {
                alreadyShown.add(dep); // Mark as shown before recursing
                printChildTree(dep, graph, alreadyShown, childPrefix, nextPrefix);
            }
        }
    }

    /**
     * Print child dependency tree recursively.
     */
    private static void printChildTree(
            String node,
            Map<String, Set<String>> graph,
            Set<String> alreadyShown,
            String currentPrefix,
            String nextPrefix) {

        System.out.println(currentPrefix + node);

        Set<String> dependencies = graph.getOrDefault(node, Set.of());
        if (dependencies.isEmpty()) {
            return;
        }

        // Sort dependencies for consistent output
        List<String> sortedDeps = dependencies.stream().sorted().collect(Collectors.toList());

        for (int i = 0; i < sortedDeps.size(); i++) {
            String dep = sortedDeps.get(i);
            boolean isLast = (i == sortedDeps.size() - 1);

            String childPrefix = nextPrefix + (isLast ? "└── " : "├── ");
            String childNextPrefix = nextPrefix + (isLast ? "    " : "│   ");

            if (alreadyShown.contains(dep)) {
                System.out.println(childPrefix + dep + " (already shown)");
            } else {
                alreadyShown.add(dep); // Mark as shown before recursing
                printChildTree(dep, graph, alreadyShown, childPrefix, childNextPrefix);
            }
        }
    }

    /**
     * Check if a dependency is a JDK dependency.
     */
    private static boolean isJdkDependency(String dependency) {
        return dependency.startsWith("java.")
                || dependency.startsWith("javax.")
                || dependency.startsWith("jdk.")
                || dependency.startsWith("sun.")
                || dependency.startsWith("com.sun.");
    }

    /**
     * Check if source and dependency are in the same scope (package/module).
     */
    private static boolean isSameScopeAsSelf(
            String source, String dependency, jarinker.core.AnalyzerType analysisType) {
        return switch (analysisType) {
            case PACKAGE -> {
                String sourcePackage = extractPackageName(source);
                String depPackage = extractPackageName(dependency);
                // Check if they are in the same package hierarchy
                yield sourcePackage.equals(depPackage)
                        || sourcePackage.startsWith(depPackage + ".")
                        || depPackage.startsWith(sourcePackage + ".");
            }
            case MODULE -> extractModuleName(source).equals(extractModuleName(dependency));
            case CLASS -> false; // For class analysis, don't filter same-scope dependencies
        };
    }

    /**
     * Extract package name from a class or package name.
     */
    private static String extractPackageName(String name) {
        if (name.contains("/")) {
            // Handle jar/class format like "jar-name/com.example.Class"
            String[] parts = name.split("/", 2);
            if (parts.length > 1) {
                name = parts[1];
            }
        }

        // For package analysis, return the full package name
        return name;
    }

    /**
     * Extract module name from a module or class name.
     */
    private static String extractModuleName(String name) {
        if (name.contains("/")) {
            // Handle jar/module format
            return name.split("/")[0];
        }

        // For module names, typically the first part before the first dot
        int firstDot = name.indexOf('.');
        return firstDot > 0 ? name.substring(0, firstDot) : name;
    }
}
